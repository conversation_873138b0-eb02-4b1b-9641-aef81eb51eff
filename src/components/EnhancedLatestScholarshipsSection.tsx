import React, { useRef, useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';
import { Scholarship } from './ScholarshipGrid';
import { useLanguage } from '../context/LanguageContext';

interface EnhancedLatestScholarshipsSectionProps {
  scholarships: Scholarship[];
  loading: boolean;
  onScholarshipClick: (id: number) => void;
}

const EnhancedLatestScholarshipsSection: React.FC<EnhancedLatestScholarshipsSectionProps> = ({
  scholarships,
  loading,
  onScholarshipClick
}) => {
  const sectionRef = useRef<HTMLElement>(null);
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const { translations } = useLanguage();

  // Filter options
  const filters = [
    { id: 'all', label: translations.scholarships.filters.all },
    { id: 'open', label: translations.scholarships.filters.open },
    { id: 'urgent', label: translations.scholarships.filters.urgent },
    ...translations.scholarships.levels.map(level => ({
      id: level.value,
      label: level.label
    }))
  ];

  // Filter scholarships based on active filter
  const filteredScholarships = scholarships.filter(scholarship => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'open') return scholarship.isOpen;
    if (activeFilter === 'urgent') {
      const deadline = new Date(scholarship.deadline);
      const today = new Date();
      const diffTime = deadline.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return scholarship.isOpen && diffDays <= 7;
    }

    // For level filters, check if the level exactly matches or contains only the specific level
    // This prevents scholarships with multiple levels from appearing in multiple categories
    if (activeFilter === 'licence') {
      const level = scholarship.level?.toLowerCase() || '';
      // Check for exact match or specific mentions
      return level === 'licence' ||
             level === 'undergraduate' ||
             level === 'bachelor' ||
             (level.includes('licence') && !level.includes('master') && !level.includes('doctorat')) ||
             (level.includes('undergraduate') && !level.includes('graduate') && !level.includes('phd'));
    }
    if (activeFilter === 'master') {
      const level = scholarship.level?.toLowerCase() || '';
      // Check for exact match or specific mentions
      return level === 'master' ||
             level === 'graduate' ||
             (level.includes('master') && !level.includes('licence') && !level.includes('doctorat')) ||
             (level.includes('graduate') && !level.includes('undergraduate') && !level.includes('phd'));
    }
    if (activeFilter === 'doctorat') {
      const level = scholarship.level?.toLowerCase() || '';
      // Check for exact match or specific mentions
      return level === 'doctorat' ||
             level === 'phd' ||
             level === 'doctorate' ||
             (level.includes('doctorat') && !level.includes('licence') && !level.includes('master')) ||
             (level.includes('phd') && !level.includes('undergraduate') && !level.includes('graduate'));
    }
    return false;
  });

  return (
    <section
      ref={sectionRef}
      id="latest-scholarships"
      className="py-10 bg-gray-50"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header - Professional & Clean */}
        <div className="mb-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-3">
            {translations.home.latestScholarships.title}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {translations.home.latestScholarships.subtitle}
          </p>
        </div>

        {/* Filter tabs */}
        <div className="mb-8 flex flex-wrap justify-center md:justify-start gap-2">
          {filters.map((filter) => (
            <button
              key={filter.id}
              onClick={() => setActiveFilter(filter.id)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                activeFilter === filter.id
                  ? 'bg-primary text-white shadow-md'
                  : 'bg-white text-gray-700 hover:bg-gray-100'
              }`}
            >
              {filter.label}
            </button>
          ))}
        </div>

        {/* Scholarship grid with loading state - 3x2 grid */}
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-md overflow-hidden animate-pulse">
                <div className="aspect-[16/9] bg-gray-200"></div>
                <div className="p-5">
                  <div className="h-5 bg-gray-200 rounded w-3/4 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-full mb-3"></div>
                  <div className="h-10 bg-gray-100 rounded w-full mt-4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredScholarships.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredScholarships.slice(0, 6).map((scholarship, index) => (
              <EnhancedScholarshipCard
                key={scholarship.id}
                id={scholarship.id}
                title={scholarship.title}
                thumbnail={scholarship.thumbnail}
                deadline={scholarship.deadline}
                isOpen={scholarship.isOpen}
                level={scholarship.level}
                fundingSource={scholarship.fundingSource}
                country={scholarship.country}
                onClick={onScholarshipClick}
                featured={false}
                index={index}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-white rounded-2xl shadow-sm">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-2 text-lg font-medium text-gray-900">{translations.scholarships.noResults.title}</h3>
            <p className="mt-1 text-gray-500">{translations.scholarships.noResults.message}</p>
            <div className="mt-6">
              <button
                onClick={() => setActiveFilter('all')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary bg-primary/10 hover:bg-primary/20"
              >
                {translations.home.latestScholarships.viewAll}
              </button>
            </div>
          </div>
        )}

        {/* Call to action */}
        <div className="mt-8 flex justify-center">
          <Link
            to="/scholarships"
            className="inline-flex items-center px-5 py-2 border border-transparent text-base font-medium rounded-xl text-white bg-primary shadow-md hover:bg-primary-dark transition-colors duration-300"
          >
            {translations.home.latestScholarships.viewAll}
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default EnhancedLatestScholarshipsSection;
